# Nouvelle Architecture Modulaire

## 🏗️ Vision architecturale

L'architecture a été complètement refactorisée pour être **modulaire, autonome et évolutive**.

### Principe de base
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Module Gabin   │    │ Module Companion│    │   Module OBS    │
│   (autonome)    │    │   (autonome)    │    │   (autonome)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Hub Central   │ ◀── Orchestration pure
                    │  (LinkManager)  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Fichiers de     │ ◀── Logique de liaison
                    │ Liaison         │     (activables/désactivables)
                    └─────────────────┘
```

## 🧩 Modules autonomes

### Chaque module est complètement indépendant :

1. **Gère sa propre connexion** (WebSocket, OSC, etc.)
2. **Maintient ses propres états** (micros, autocam, etc.)
3. **Expose des interfaces standardisées** (IMicrophoneModule, IAutocamModule, etc.)
4. **Émet des événements** pour communiquer ses changements d'état
5. **Peut être activé/désactivé** via configuration

### Modules disponibles :

#### 📹 GabinModuleService
- **Responsabilité** : Communication avec Gabin via OSC
- **États** : Autocam, micros
- **Interfaces** : `IMicrophoneModule`, `IAutocamModule`
- **Configuration** : `modulesConfig.gabin.enabled`

#### 🎥 OBSModuleService  
- **Responsabilité** : Communication avec OBS via WebSocket
- **États** : Sources audio, volumes, mute
- **Interfaces** : `IMicrophoneModule` + méthodes OBS spécifiques
- **Configuration** : `modulesConfig.obs.enabled`

#### 🎛️ CompanionModuleService
- **Responsabilité** : Communication avec Companion via OSC
- **États** : Feedbacks, actions reçues
- **Interfaces** : `IControlModule`
- **Configuration** : `modulesConfig.companion.enabled`

## 🔗 Système de liens

### Le Hub ne fait QUE de l'orchestration

Le `HubService` ne contient plus aucune logique métier. Il ne fait que :
- Enregistrer les modules
- Gérer les liens entre modules
- Fournir une API de statut global

### Fichiers de liaison

La logique de communication entre modules est dans des **fichiers de liaison séparés** :

#### `gabin-to-companion.link.ts`
```typescript
// Synchronise les états Gabin → Companion
gabin.onEvent('mic_changed') → companion.sendFeedback('mic', ...)
gabin.onEvent('autocam_changed') → companion.sendFeedback('autocam', ...)
```

#### `companion-to-gabin.link.ts`
```typescript
// Transmet les commandes Companion → Gabin
companion.onEvent('companion_action') → gabin.setMicState(...)
```

#### `companion-to-obs.link.ts`
```typescript
// Transmet les commandes Companion → OBS
companion.onEvent('companion_action') → obs.setSourceMute(...)
```

### Configuration des liens

```typescript
// Dans LinkManagerService
this.linkConfigs.set('gabin-to-companion', {
    enabled: true,  // ✅ Actif
    description: 'Synchronise Gabin vers Companion',
    options: { syncAutocam: true, syncMics: true }
});

this.linkConfigs.set('companion-to-obs', {
    enabled: false, // ❌ Désactivé par défaut
    description: 'Contrôle OBS via Companion',
    options: { allowVolumeControl: true }
});
```

## 🎯 Avantages de cette architecture

### 1. **Modularité parfaite**
- Chaque module peut être développé/testé indépendamment
- Ajout de nouveaux modules sans impact sur l'existant
- Suppression facile d'un module

### 2. **Configuration flexible**
```typescript
// Désactiver Gabin, activer OBS
modulesConfig.gabin.enabled = false;
modulesConfig.obs.enabled = true;

// Désactiver le lien Gabin→Companion, activer OBS→Companion
linkConfigs['gabin-to-companion'].enabled = false;
linkConfigs['obs-to-companion'].enabled = true;
```

### 3. **Évolutivité**
- Nouveaux modules : créer une classe qui implémente `IModule`
- Nouvelles liaisons : créer un fichier `.link.ts`
- Nouvelles fonctionnalités : étendre les interfaces

### 4. **Maintenance simplifiée**
- Logique métier isolée dans chaque module
- Logique de communication isolée dans les liens
- Hub central minimal et stable

## ✅ Migration terminée !

### Étapes de migration :

1. ✅ **Interfaces et base** : `IModule`, `BaseModule`, `LinkManager`
2. ✅ **Modules autonomes** : `GabinModuleService`, `OBSModuleService`, `CompanionModuleService`
3. ✅ **Fichiers de liaison** : `gabin-to-companion.link.ts`, etc.
4. ✅ **Adaptation des modules existants** : Anciens services supprimés
5. ✅ **Tests et validation** : Architecture fonctionnelle
6. ✅ **Nettoyage** : Code obsolète supprimé

### Configuration actuelle

```typescript
// modules.local.ts
{
    gabin: { enabled: false },    // Désactivé
    obs: { enabled: true },       // Actif
    companion: { enabled: true }  // Actif
}

// Liens actifs (à configurer) :
// - companion-to-obs : Contrôler OBS via Companion
// - obs-to-companion : Feedbacks OBS vers Companion
```

## 🎛️ Utilisation

### Ajouter un nouveau module
```typescript
@Injectable()
export class MonNouveauModule extends BaseModule implements IModule {
    constructor() {
        super('mon-module', config.enabled);
    }
    
    async start() { /* logique de démarrage */ }
    async stop() { /* logique d'arrêt */ }
}
```

### Ajouter un nouveau lien
```typescript
export class MonNouveauLink implements IModuleLink {
    async initialize(modules: Map<string, IModule>) {
        const moduleA = modules.get('module-a');
        const moduleB = modules.get('module-b');
        
        moduleA.onEvent((event) => {
            // Logique de liaison
            moduleB.doSomething(event.data);
        });
    }
}
```

### Activer/désactiver un lien
```typescript
// Via l'API Hub
await hubService.toggleLink('companion-to-obs', true);
```

Cette architecture permet une **flexibilité maximale** tout en gardant le code **simple et maintenable** ! 🎉
