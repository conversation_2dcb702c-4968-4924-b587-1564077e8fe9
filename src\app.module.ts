import { Module, DynamicModule } from '@nestjs/common';
import { AppController } from './app.controller';
import { HubModule } from './core/hub.module';
import { AutonomousModulesModule } from './core/autonomous-modules.module';

@Module({
    controllers: [AppController],
})
export class AppModule {
    static forRoot(): DynamicModule {
        // Architecture modulaire autonome
        console.log('[app] Loading autonomous modules...');

        return {
            module: AppModule,
            imports: [HubModule, AutonomousModulesModule],
        };
    }
}
