# Test de l'intégration OBS

## ✅ Intégration terminée et refactorisée !

L'intégration OBS est maintenant fonctionnelle dans votre Hub avec une architecture claire et simplifiée. Voici comment la tester :

## 🏗️ Architecture modulaire autonome

**Responsabilités clarifiées :**
- **HubService** : Orchestration pure des liens entre modules
- **OBSModuleService** : Module autonome OBS avec logique métier intégrée
- **OBSController** : Interface REST pour contrôler OBS
- **LinkManager** : Gestion des liens inter-modules

**Avantages :**
- ✅ Modules complètement autonomes
- ✅ Séparation claire des responsabilités
- ✅ Code plus maintenable et évolutif
- ✅ Configuration flexible des modules

## 🚀 Démarrage

1. **Démarrer OBS Studio** avec WebSocket activé
   - Port configuré : **4444** (voir votre config locale)
   - Mot de passe : **vince1992** (voir votre config locale)
2. **Démarrer le Hub** : `npm run start:dev`
3. **Vérifier la connexion** : Le Hub devrait afficher `[obs] Connected to OBS WebSocket`

## 🎛️ API disponible pour contrôler OBS

### Contrôle du volume de "MIC Invite 1"

```bash
# Obtenir les informations de la source
GET http://localhost:3000/obs/source/MIC%20Invite%201/info

# Définir le volume en dB (-100 à +26)
POST http://localhost:3000/obs/source/MIC%20Invite%201/volume
Content-Type: application/json
{ "volumeDb": -10 }

# Définir le volume en pourcentage (0 à 100)
POST http://localhost:3000/obs/source/MIC%20Invite%201/volume-percent
Content-Type: application/json
{ "volumePercent": 50 }

# Ajuster le volume relativement (comme dans Companion)
POST http://localhost:3000/obs/source/MIC%20Invite%201/adjust-volume
Content-Type: application/json
{ "percentAdjustment": -100 }  # -100% = mute, +50% = augmente de 50%

# Mute/Unmute
POST http://localhost:3000/obs/source/MIC%20Invite%201/mute
Content-Type: application/json
{ "muted": true }

# Toggle mute
POST http://localhost:3000/obs/source/MIC%20Invite%201/toggle-mute
```

## 🔄 Remplacement des fonctions Companion

Au lieu d'utiliser les actions OBS directement dans Companion, vous pouvez maintenant :

1. **Configurer Companion** pour envoyer des requêtes HTTP vers votre Hub
2. **Centraliser la logique** dans le Hub pour des automations plus complexes
3. **Ajouter des conditions** (ex: ajuster le volume seulement si un micro est actif)

## 🧪 Test rapide avec PowerShell

```powershell
# Vérifier le statut (OBS doit être connecté)
Invoke-RestMethod -Uri 'http://localhost:3000/status'

# Tester le contrôle de volume
Invoke-RestMethod -Uri 'http://localhost:3000/obs/source/MIC%20Invite%201/adjust-volume' -Method POST -Body '{"percentAdjustment": -100}' -ContentType 'application/json'
```

## 📋 Prochaines étapes possibles

1. **Automations** : Connecter les événements de micros aux contrôles OBS
2. **Presets caméras** : Ajouter le contrôle des caméras Obsbot via Companion
3. **Scènes OBS** : Ajouter le contrôle des scènes et transitions
4. **Interface web** : Créer une interface de contrôle centralisée

## 🔧 Configuration

La configuration OBS se trouve dans :
- `src/config/defaults/obs.default.ts` (ne pas modifier)
- `src/config/local/obs.local.ts` (pour vos paramètres)

Exemple de configuration locale :
```typescript
export const obsLocalConfig: Partial<OBSConfig> = {
    network: {
        host: '*************',  // IP d'OBS si sur une autre machine
        port: 4455,             // Port WebSocket d'OBS
        password: 'your-password' // Mot de passe OBS WebSocket
    }
};
```
