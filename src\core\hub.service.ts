import { Injectable, OnModuleInit } from '@nestjs/common';
import { LinkManagerService } from './services/link-manager.service';

/**
 * Hub Central - Orchestration pure
 *
 * Responsabilité UNIQUE : Gérer les liens entre modules autonomes
 */
@Injectable()
export class HubService implements OnModuleInit {
    constructor(
        private readonly linkManager: LinkManagerService,
    ) {}

    async onModuleInit() {
        console.log('[Hub] Hub orchestration initialized - managing module links only');
    }

    /**
     * Obtenir l'état de tous les modules (pour l'API /status)
     */
    getModulesStatus(): Record<string, any> {
        return this.linkManager.getModulesStatus();
    }

    /**
     * Obtenir l'état de tous les liens (pour l'API /status)
     */
    getLinksStatus(): Record<string, any> {
        return this.linkManager.getLinksStatus?.() || {};
    }

    /**
     * Activer/désactiver un lien entre modules
     */
    async toggleLink(linkName: string, enabled: boolean): Promise<void> {
        return this.linkManager.toggleLink(linkName, enabled);
    }

    /**
     * API de statut global du Hub
     */
    getStatus(): any {
        return {
            modules: this.getModulesStatus(),
            links: this.getLinksStatus(),
        };
    }
}
